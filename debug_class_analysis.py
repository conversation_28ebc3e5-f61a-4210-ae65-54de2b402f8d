#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
班级分析功能调试脚本
"""

import sqlite3
from models.scholarship import ScholarshipData

def check_database_data():
    """检查数据库中的数据"""
    print("=" * 50)
    print("数据库数据检查")
    print("=" * 50)
    
    try:
        # 连接数据库
        conn = sqlite3.connect('scholarship_data.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 检查表结构
        print("\n📋 数据库表结构:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        for table in tables:
            print(f"  - {table['name']}")
        
        # 检查专业数据
        print("\n🎓 专业数据:")
        cursor.execute("SELECT DISTINCT major_name FROM majors ORDER BY major_name")
        majors = cursor.fetchall()
        print(f"  专业总数: {len(majors)}")
        for major in majors[:5]:
            print(f"    - {major['major_name']}")
        if len(majors) > 5:
            print(f"    ... 还有 {len(majors) - 5} 个专业")
        
        # 检查学期数据
        print("\n📅 学期数据:")
        cursor.execute("SELECT DISTINCT academic_year, semester FROM semesters ORDER BY academic_year DESC, semester DESC")
        semesters = cursor.fetchall()
        print(f"  学期总数: {len(semesters)}")
        for sem in semesters[:5]:
            print(f"    - {sem['academic_year']}-{sem['semester']}")
        
        # 检查班级数据
        print("\n🏫 班级数据:")
        cursor.execute("SELECT DISTINCT class_name FROM scores ORDER BY class_name")
        classes = cursor.fetchall()
        print(f"  班级总数: {len(classes)}")
        for cls in classes[:10]:
            print(f"    - {cls['class_name']}")
        if len(classes) > 10:
            print(f"    ... 还有 {len(classes) - 10} 个班级")
        
        # 检查成绩数据
        print("\n📊 成绩数据:")
        cursor.execute("SELECT COUNT(*) as count FROM scores")
        score_count = cursor.fetchone()['count']
        print(f"  成绩记录总数: {score_count}")
        
        # 检查最新学期的班级统计
        print("\n📈 最新学期班级统计:")
        cursor.execute("""
            SELECT 
                sc.class_name,
                COUNT(*) as student_count,
                AVG(sc.total_score) as avg_total_score,
                AVG(sc.academic_score) as avg_academic_score,
                AVG(sc.comprehensive_score) as avg_comprehensive_score
            FROM scores sc
            JOIN semesters sem ON sc.semester_id = sem.id
            WHERE sem.academic_year = '2024-2025' AND sem.semester = 1
            GROUP BY sc.class_name 
            ORDER BY avg_total_score DESC
            LIMIT 5
        """)
        class_stats = cursor.fetchall()
        
        if class_stats:
            print(f"  2024-2025第一学期班级数: {len(class_stats)}")
            for cls in class_stats:
                print(f"    - {cls['class_name']}: {cls['student_count']}人, 平均分: {cls['avg_total_score']:.2f}")
        else:
            print("  ❌ 未找到2024-2025第一学期的数据")
            
            # 检查所有可用的学期
            cursor.execute("""
                SELECT DISTINCT sem.academic_year, sem.semester, COUNT(*) as record_count
                FROM scores sc
                JOIN semesters sem ON sc.semester_id = sem.id
                GROUP BY sem.academic_year, sem.semester
                ORDER BY sem.academic_year DESC, sem.semester DESC
            """)
            available_semesters = cursor.fetchall()
            print("\n  📅 可用的学期数据:")
            for sem in available_semesters:
                print(f"    - {sem['academic_year']}-{sem['semester']}: {sem['record_count']}条记录")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def test_scholarship_data_methods():
    """测试ScholarshipData类的方法"""
    print("\n" + "=" * 50)
    print("ScholarshipData方法测试")
    print("=" * 50)
    
    try:
        scholarship_data = ScholarshipData()
        
        # 测试get_available_filters
        print("\n🔍 测试get_available_filters方法:")
        filters = scholarship_data.get_available_filters()
        print(f"  学期数: {len(filters.get('semesters', []))}")
        print(f"  专业数: {len(filters.get('majors', []))}")
        print(f"  年级数: {len(filters.get('grades', []))}")
        
        if filters.get('majors'):
            print("  专业示例:")
            for major in filters['majors'][:3]:
                print(f"    - {major}")
        
        # 测试get_class_analysis_data
        print("\n📊 测试get_class_analysis_data方法:")
        
        # 使用第一个可用的学期
        if filters.get('semesters'):
            first_semester = filters['semesters'][0]
            academic_year = first_semester['academic_year']
            semester = first_semester['semester']
            
            print(f"  使用学期: {academic_year}-{semester}")
            
            class_data = scholarship_data.get_class_analysis_data(
                academic_year=academic_year,
                semester=semester
            )
            
            print(f"  返回班级数: {len(class_data)}")
            
            if class_data:
                print("  班级数据示例:")
                for cls in class_data[:3]:
                    print(f"    - {cls['class_name']}: {cls['student_count']}人, 平均分: {cls['avg_total_score']:.2f}")
                return True
            else:
                print("  ❌ 未返回班级数据")
                return False
        else:
            print("  ❌ 没有可用的学期数据")
            return False
            
    except Exception as e:
        print(f"❌ ScholarshipData方法测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 班级分析功能调试")
    
    # 检查数据库数据
    db_ok = check_database_data()
    
    # 测试ScholarshipData方法
    methods_ok = test_scholarship_data_methods()
    
    print("\n" + "=" * 50)
    print("调试结果汇总")
    print("=" * 50)
    print(f"数据库数据检查: {'✅ 通过' if db_ok else '❌ 失败'}")
    print(f"ScholarshipData方法测试: {'✅ 通过' if methods_ok else '❌ 失败'}")
    
    if db_ok and methods_ok:
        print("\n🎉 数据层功能正常，问题可能在前端或API层")
    else:
        print("\n⚠️  发现数据层问题，需要修复")

if __name__ == '__main__':
    main()
